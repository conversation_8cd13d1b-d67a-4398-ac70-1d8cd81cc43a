import { ArrowRight, Bank, Moneys } from 'iconsax-react';
import { useState, useEffect } from 'react';
import { TextInput } from '../../../../components/inputs/text-input/text-input';
import { useQuery } from '@tanstack/react-query';
import { GiftRegistryServices } from '../../../../lib/services/gift-registry';

interface AccountDetailsProps {
  onNextStep: (data: { bank: string; accountNumber: string }) => void;
  initialData?: { bank?: string; accountNumber?: string };
}

interface Bank {
  bank_code: string;
  bank_name: string;
}

export const AccountSetup = ({
  onNextStep,
  initialData = {},
}: AccountDetailsProps) => {
  const [bank, setBank] = useState(initialData.bank || '');
  const [accountNumber, setAccountNumber] = useState(
    initialData.accountNumber || ''
  );

  const {
    data: banks,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['payoutBanks'],
    queryFn: GiftRegistryServices.getPayoutBanks,
  });

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const handleContinue = () => {
    onNextStep({
      bank,
      accountNumber,
    });
  };

  return (
    <div className="px-4 md:px-0 md:ml-3.5">
      <div className="max-w-[550px] mx-auto mb-32 mt-9">
        <h2 className="md:text-[40px] text-2xl font-medium -tracking-[0.03em]">
          Add Account details
        </h2>
        <div className="bg-white rounded-[20px] mt-20 px-5 pb-6 w-full">
          <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-cus-pink-900 flex items-center justify-center cursor-pointer relative overflow-hidden">
            <input
              type="file"
              accept="image/*"
              className="absolute inset-0 opacity-0 cursor-pointer z-10"
            />

            <Moneys variant="Bulk" size="62" color="#FFAA8C" />
          </div>

          <div className="-mt-12">
            <div className="bg-cus-pink flex items-center gap-3 p-3.5 mb-6 rounded-2xl">
              <Bank color="#F7BFA9" size={56} variant="Bulk" />
              <p className="italic text-dark-blue-500 font-medium text-xs md:text-sm">
                Add your bank details so guests can easily contribute cash if
                <br /> they prefer to gift money instead of purchasing an item.
              </p>
            </div>
            <div className="mb-6">
              <label className="block text-grey-500 font-medium text-sm mb-2">
                Bank
              </label>
              <select
                value={bank}
                onChange={(e) => setBank(e.target.value)}
                className="w-full h-[44px] px-3.5 border border-gray-300 rounded-full text-base font-bold text-grey-50 italic outline-0"
                disabled={isLoading}>
                {/* <option value="">Select Bank</option> */}
                {isLoading ? (
                  <option value="" disabled>
                    Loading banks...
                  </option>
                ) : error ? (
                  <option value="" disabled>
                    Error loading banks
                  </option>
                ) : (
                  banks?.data?.map((bank: Bank) => (
                    <option key={bank?.bank_code} value={bank?.bank_code}>
                      {bank?.bank_name}
                    </option>
                  ))
                )}
              </select>
              {error && (
                <span className="text-xs italic text-red-500">
                  Failed to load banks. Please try again.
                </span>
              )}
            </div>
            <div className="mb-6">
              <TextInput
                id="accountNumber"
                label="Account Number"
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.target.value)}
                placeholder="e.g **********"
                className="text-grey-50 font-bold italic placeholder:font-normal placeholder:text-grey-700"
              />
              <span className="text-xs italic text-grey-650">
                Enter account number to receive cash gift
              </span>
            </div>
            <button
              onClick={handleContinue}
              disabled={!bank || !accountNumber || isLoading}
              className={`bg-primary-650 text-white py-2.5 px-4 mb-0 rounded-full cursor-pointer flex items-center gap-2 ${
                bank && accountNumber && !isLoading
                  ? ''
                  : 'opacity-50 cursor-not-allowed'
              }`}>
              {isLoading ? 'Loading...' : 'Continue'}
              <div className="bg-white/30 rounded-full p-0.5">
                <ArrowRight size="12" color="#fff" />
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
