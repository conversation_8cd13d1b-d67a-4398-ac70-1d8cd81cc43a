import { EventParkAPI } from '../event-park-api';

export interface CashGiftItem {
  amount: string;
  description: string;
  is_crowd_gift?: boolean;
}

export interface CreateCashGiftPayload {
  gifts: CashGiftItem[];
}

export const GiftRegistryServices = {
  createCashGift: async (eventId: string, payload: CreateCashGiftPayload) => {
    return await EventParkAPI().post(
      `/v1/user/events/${eventId}/cash-gifts`,
      payload
    );
  },
  getPayoutBanks: async () => {
    return await EventParkAPI().get('/v1/user/payout/banks');
  },
  resolveUserPayoutBank: async (payload: {
    bank_code: string;
    account_number: string;
  }) => {
    return await EventParkAPI().post(
      '/v1/user/payout/accounts/resolve',
      payload
    );
  },
};
